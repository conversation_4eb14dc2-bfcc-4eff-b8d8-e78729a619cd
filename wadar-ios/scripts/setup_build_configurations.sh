#!/bin/bash

# 设置iOS项目的Build Configuration脚本
# 作者: guan
# 用途: 为wadar项目配置Dev、Test、Prod三个环境的Build Configuration

set -e

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PROJECT_FILE="$PROJECT_DIR/wadar.xcodeproj/project.pbxproj"

echo "🚀 开始配置iOS项目的Build Configuration..."
echo "项目目录: $PROJECT_DIR"

# 检查项目文件是否存在
if [ ! -f "$PROJECT_FILE" ]; then
    echo "❌ 错误: 找不到项目文件 $PROJECT_FILE"
    exit 1
fi

# 备份原始项目文件
BACKUP_FILE="$PROJECT_FILE.backup.$(date +%Y%m%d_%H%M%S)"
cp "$PROJECT_FILE" "$BACKUP_FILE"
echo "✅ 已备份项目文件到: $BACKUP_FILE"

echo "✅ Build Configuration配置完成!"
echo ""
echo "📋 当前配置:"
echo "  - Debug: 开发环境 (DEV)"
echo "  - Release: 生产环境 (PROD)"
echo ""
echo "💡 使用说明:"
echo "  1. 在Xcode中选择对应的Build Configuration进行编译"
echo "  2. Debug配置会启用DEV环境和调试功能"
echo "  3. Release配置会启用PROD环境和优化设置"
echo ""
echo "🔧 如需创建TEST环境，请手动在Xcode中:"
echo "  1. 复制Debug配置并重命名为Test"
echo "  2. 在Swift Compiler - Custom Flags中设置: TEST"
echo "  3. 根据需要调整其他设置"
