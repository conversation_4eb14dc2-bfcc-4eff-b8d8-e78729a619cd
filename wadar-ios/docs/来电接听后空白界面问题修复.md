# 来电接听后空白界面问题修复

## 问题描述

用户反馈：来电弹出来电界面，但是接听后显示空白界面，顶部还有个Back按钮，没有进入到通话界面。

## 问题分析

通过代码分析发现了两个关键问题：

### 1. 来电状态处理缺少通知

在`CallViewModel.swift`中，来电状态变为`Connected`时，只是设置了内部状态，但**没有发送`callConnected`通知**：

```swift
} else if (state == .Connected) { // 通话建立时
    self.isCallIncoming = false
    self.isIncomingCallRunning = true
    
    // 清除相关通知
    PushNotificationManager.shared.clearNotification(identifier: "incoming_call")
    
    // ❌ 缺少这行：没有发送通话连接通知
    // NotificationCenter.default.post(name: .callConnected, object: nil)
}
```

### 2. 来电界面过早dismiss

在`CallIncomingView.swift`中，接听按钮点击后立即dismiss了界面：

```swift
func answerCall() {
    self.viewModel.acceptCall()
    // ❌ 问题：立即dismiss，不等待通话状态变化
    presentationMode.wrappedValue.dismiss()
}
```

这导致接听后界面消失，但NavigationManager没有收到通知去跳转到通话界面，所以显示空白。

## 解决方案

### 1. 修复CallViewModel - 添加通话连接通知

在来电状态变为`Connected`时，发送`callConnected`通知：

```swift
} else if (state == .Connected) { // 通话建立时
    self.isCallIncoming = false
    self.isIncomingCallRunning = true

    // 清除相关通知
    PushNotificationManager.shared.clearNotification(identifier: "incoming_call")
    
    // ✅ 修复：发送通话连接成功通知，触发导航到通话界面
    NotificationCenter.default.post(name: .callConnected, object: nil)
}
```

### 2. 修复CallIncomingView - 移除过早dismiss

让通话状态变化来驱动界面跳转，而不是手动dismiss：

```swift
func answerCall() {
    // 接听通话逻辑，实际项目中请调用VoIP SDK
    self.viewModel.acceptCall()
    // ✅ 修复：不要立即dismiss，等待通话状态变为Connected后
    // CallViewModel会发送callConnected通知，NavigationManager会处理界面跳转
    // presentationMode.wrappedValue.dismiss() // 移除这行，让导航管理器处理
}
```

## 修复流程

修复后的正确流程：

1. **用户点击接听** → `CallIncomingView.answerCall()`
2. **调用SDK接听** → `viewModel.acceptCall()`
3. **等待通话建立** → Linphone SDK状态变化
4. **状态变为Connected** → `CallViewModel`收到状态变化
5. **发送通话连接通知** → `NotificationCenter.post(.callConnected)`
6. **NavigationManager处理** → `handleCallConnected()`
7. **跳转到通话界面** → `replace(with: .callConnected)`

## 测试验证

修复后应该能够：

1. ✅ 来电界面正常显示
2. ✅ 点击接听后等待通话建立
3. ✅ 通话建立后自动跳转到通话界面
4. ✅ 通话界面显示正确的通话信息
5. ✅ 通话结束后返回WebView首页

## 相关文件

- `wadar-ios/wadar/ViewModel/CallViewModel.swift` - 添加来电连接通知
- `wadar-ios/wadar/Views/CallIncomingView.swift` - 移除过早dismiss
- `wadar-ios/wadar/Managers/NavigationManager.swift` - 处理通话连接导航

## 注意事项

这个修复确保了：
- 通话状态变化驱动界面跳转
- 避免了手动界面管理的复杂性
- 保持了一致的导航逻辑
- 符合响应式编程的原则
