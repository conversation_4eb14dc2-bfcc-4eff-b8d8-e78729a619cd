# iOS应用自动登录功能测试指南

## 功能概述
实现了iOS应用启动时的静默自动登录功能，包括：
1. 应用启动时显示加载界面，后台检查本地存储的token
2. 如果token有效，自动登录并直接进入主页面（H5页面）
3. 同时自动登录VoIP账号
4. 如果token无效，清除本地数据并显示登录界面
5. 在API调用过程中检测token失效，自动跳转到登录界面
6. **避免闪现登录页面**：只有确认需要登录时才显示登录界面

## 测试场景

### 场景1：首次启动（无本地token）
**预期行为：**
- 应用启动后显示"正在启动..."加载界面
- 后台检查发现无本地token
- 自动切换到登录界面
- 用户需要手动输入账号密码登录

**测试步骤：**
1. 删除应用重新安装，或清除应用数据
2. 启动应用
3. 验证是否先显示加载界面，然后切换到登录界面
4. 确认没有闪现其他页面

### 场景2：有效token自动登录
**预期行为：**
- 应用启动后显示"正在启动..."加载界面
- 后台检查token有效性
- Token有效时自动登录成功
- 直接进入H5页面（不显示登录界面）
- 自动登录VoIP账号

**测试步骤：**
1. 先手动登录一次，确保token被保存
2. 关闭应用
3. 重新启动应用
4. 验证是否显示加载界面后直接进入H5页面
5. 确认没有显示登录界面
6. 检查VoIP登录状态

### 场景3：无效token处理
**预期行为：**
- 应用启动后显示"正在启动..."加载界面
- 后台检查发现token无效
- 自动清除本地用户数据
- 切换到登录界面，不显示错误提示

**测试步骤：**
1. 手动修改本地token为无效值，或等待token过期
2. 启动应用
3. 验证是否显示加载界面后切换到登录界面
4. 确认没有显示错误提示

### 场景4：API调用中token失效
**预期行为：**
- 在使用过程中，如果API返回token失效错误
- 自动清除本地用户数据
- 自动跳转到登录界面
- 清除VoIP登录状态

**测试步骤：**
1. 正常登录并使用应用
2. 在后台使其他设备登录同一账号（使当前token失效）
3. 在当前设备进行需要token的操作
4. 验证是否自动跳转到登录界面

## 关键实现点

### 1. ContentView启动流程
```swift
var body: some View {
    ZStack {
        if isCheckingAutoLogin {
            // 显示启动加载界面
            VStack {
                ProgressView()
                Text("正在启动...")
            }
        } else if !loginVM.loggedIn {
            LoginView(viewModel: loginVM)
        } else {
            // 主页面内容
        }
    }
    .onAppear {
        // 应用启动时检查自动登录
        loginVM.checkAutoLogin { success in
            self.isCheckingAutoLogin = false
        }
    }
}
```

### 2. LoginViewModel自动登录方法
```swift
func checkAutoLogin(completion: @escaping (Bool) -> Void = { _ in }) {
    // 检查本地保存的用户数据
    guard let savedUser = UserManager.shared.getUser() else {
        completion(false)
        return
    }

    // 校验token有效性
    // 如果有效：设置登录状态 + 跳转H5 + VoIP自动登录 + completion(true)
    // 如果无效：清除本地数据 + completion(false)
}
```

### 3. NetworkManager token失效处理
```swift
if response.message.contains("TOKEN WAS WRONG") {
    // 清除用户数据
    UserManager.shared.clearUser()
    // 发送token失效通知
    NotificationCenter.default.post(name: .TokenInvalidNotification, object: nil)
}
```

### 4. VoIP自动登录
```swift
// 在登录成功后自动登录VoIP
if let callVM = self.callVM, !callVM.loggedIn {
    if let voipNumber = userData.member.voipNumber, let voipPassword = userData.member.voipPassword {
        callVM.username = voipNumber
        callVM.passwd = voipPassword
    }
    callVM.login()
}
```

## 日志监控
在测试过程中，关注以下日志输出：
- `[LoginViewModel] 检查自动登录`
- `[LoginViewModel] 自动登录成功，token有效`
- `[LoginViewModel] 自动登录失败，token无效`
- `[CallViewModel] VoIP登录成功`
- `[NetworkManager] 检测到token失效`
- `[ContentView] 收到token失效通知`

## 注意事项
1. 自动登录失败时不会显示错误提示，用户体验更好
2. VoIP登录依赖于用户数据中的voipNumber和voipPassword字段
3. Token失效时会同时清除VoIP登录状态
4. 所有网络请求都会自动添加token到请求头
