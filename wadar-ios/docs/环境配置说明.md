# iOS项目环境配置说明

## 概述

wadar iOS项目支持多环境配置，通过Build Configuration和编译标志来切换不同的环境设置。

## 环境配置

### 1. 开发环境 (DEV)
- **Build Configuration**: Debug
- **编译标志**: DEV
- **服务器地址**: https://cmcctest.rfcare.cn
- **WebView URL**: https://cmcctest.rfcare.cn/?platform=iOS&appType=app
- **VoIP域名**: cmcctest.rfcare.cn
- **调试模式**: 启用
- **日志记录**: 启用
- **网络日志**: 启用

### 2. 测试环境 (TEST)
- **Build Configuration**: 需要手动创建Test配置
- **编译标志**: TEST
- **服务器地址**: https://testcmcc.rfcare.cn
- **WebView URL**: https://testcmcc.rfcare.cn/?platform=iOS&appType=app
- **VoIP域名**: testcmcc.rfcare.cn
- **调试模式**: 启用
- **日志记录**: 启用
- **网络日志**: 禁用

### 3. 生产环境 (PROD)
- **Build Configuration**: Release
- **编译标志**: PROD
- **服务器地址**: https://cmcc.rfcare.cn
- **WebView URL**: https://cmcc.rfcare.cn/?platform=iOS&appType=app
- **VoIP域名**: cmcc.rfcare.cn
- **调试模式**: 禁用
- **日志记录**: 禁用
- **网络日志**: 禁用

## 使用方法

### 在Xcode中切换环境

1. 打开wadar.xcworkspace
2. 在顶部工具栏中点击项目名称旁边的配置选择器
3. 选择对应的Build Configuration:
   - Debug (开发环境)
   - Release (生产环境)

### 创建TEST环境配置

1. 在Xcode中打开项目设置
2. 选择wadar项目
3. 在Info标签页中找到Configurations
4. 点击"+"按钮，选择"Duplicate Debug Configuration"
5. 重命名为"Test"
6. 在Build Settings中找到"Swift Compiler - Custom Flags"
7. 在"Other Swift Flags"中添加: `-DTEST`
8. 在"Swift Compiler - Conditional Compilation Flags"中设置为: `DEBUG TEST $(inherited)`

### 在Xcode中使用环境配置

#### 方法一：通过Build Configuration切换
1. 在Xcode顶部工具栏中，点击项目名称旁边的配置选择器
2. 选择"Edit Scheme..."
3. 在左侧选择"Run"
4. 在"Build Configuration"下拉菜单中选择：
   - **Debug**: 开发环境 (DEV)
   - **Test**: 测试环境 (TEST)
   - **Release**: 生产环境 (PROD)
5. 点击"Close"保存设置

#### 方法二：通过命令行编译
```bash
# 编译开发环境
xcodebuild -workspace wadar.xcworkspace -scheme wadar -configuration Debug

# 编译测试环境
xcodebuild -workspace wadar.xcworkspace -scheme wadar -configuration Test

# 编译生产环境
xcodebuild -workspace wadar.xcworkspace -scheme wadar -configuration Release
```

#### 验证环境配置
编译并运行应用后，可以通过以下方式验证当前环境：
1. 查看应用加载的WebView URL
2. 在开发环境下，控制台会输出环境配置信息
3. 检查网络请求的目标服务器地址

### 代码中使用环境配置

```swift
// 获取当前环境配置
let config = AppConfig.shared

// 获取基础URL
let baseURL = config.baseURL

// 获取WebView URL（带token）
let webViewURL = config.getWebViewURL(with: userToken)

// 检查是否为开发环境
if config.isDevelopmentEnvironment {
    // 开发环境特有逻辑
}

// 获取环境信息
print(config.getEnvironmentInfo())
```

## 配置文件说明

### AppConfig.swift
主要的环境配置管理类，包含：
- 环境枚举定义
- 各环境的具体配置参数
- 便利方法和属性

### project.pbxproj
Xcode项目配置文件，包含：
- Build Configuration定义
- 编译标志设置
- 其他构建设置

## 编译标志配置详解

### Swift编译条件标志设置
在项目的Build Settings中，需要正确设置以下编译标志：

#### Debug配置
- **Swift Compiler - Conditional Compilation Flags**: `DEBUG DEV $(inherited)`
- **Other Swift Flags**: 不设置

#### Test配置
- **Swift Compiler - Conditional Compilation Flags**: `DEBUG TEST $(inherited)`
- **Other Swift Flags**: `-DTEST`

#### Release配置
- **Swift Compiler - Conditional Compilation Flags**: `PROD $(inherited)`
- **Other Swift Flags**: 不设置

### 编译标志验证
可以通过以下代码验证编译标志是否正确设置：
```swift
#if DEV
print("当前为开发环境")
#elseif TEST
print("当前为测试环境")
#else
print("当前为生产环境")
#endif
```

## 注意事项

1. **编译标志**: 确保在Build Settings中正确设置了Swift编译条件标志
2. **URL配置**: 不同环境使用不同的服务器地址，确保配置正确
3. **调试设置**: 生产环境应禁用所有调试功能
4. **版本控制**: project.pbxproj文件的修改需要谨慎，建议先备份
5. **Pods配置**: 修改Build Configuration后需要运行 `pod install` 更新Pods配置

## 故障排除

### 环境配置不生效
1. 检查Build Settings中的"Swift Compiler - Custom Flags"设置
2. 确认选择了正确的Build Configuration
3. 清理项目并重新构建

### 编译错误
1. 检查编译标志是否正确设置
2. 确认所有必要的配置都已添加
3. 查看编译日志中的具体错误信息

### 网络连接错误
**问题**: TEST环境出现DNS解析失败错误
```
URLError(_nsError: Error Domain=NSURLErrorDomain Code=-1003 "A server with the specified hostname could not be found.")
```

**原因**: 配置的服务器域名不存在（如 `testcmcc.rfcare.cn`）

**解决方案**:
1. 确认服务器域名是否正确存在
2. 如果测试环境域名不存在，可以使用开发环境的域名但添加不同的参数区分
3. 示例修正：
   ```swift
   // 修正前（域名不存在）
   self.baseURL = "https://testcmcc.rfcare.cn"

   // 修正后（使用存在的域名+参数区分）
   self.baseURL = "https://cmcctest.rfcare.cn"
   self.webviewUrl = "https://cmcctest.rfcare.cn/?platform=iOS&appType=app&env=test"
   ```

### 验证环境配置
可以通过以下方式验证环境配置是否正确：

1. **查看应用日志**: 在开发环境下会输出环境配置信息
2. **检查WebView URL**: 观察应用加载的URL是否符合预期
3. **网络请求监控**: 确认API请求发送到正确的服务器地址

**成功示例**:
```
加载URL: https://cmcctest.rfcare.cn/?platform=iOS&appType=app&env=test/pages/auth/app-auth/index?platform=iOS&appType=app&token=xxx
页面标题: 与安宝
✅ 页面加载完成
```

## 相关文件

- `wadar/Modules/Common/Managers/AppConfig.swift` - 环境配置管理
- `wadar.xcodeproj/project.pbxproj` - Xcode项目配置
- `scripts/setup_build_configurations.sh` - 配置脚本
