# iOS导航架构重构说明

## 重构背景

原项目使用条件渲染（if-else）方式来切换不同页面，这种方式在简单场景下可行，但随着功能增加会带来诸多问题。为了提供更好的用户体验和代码维护性，我们将架构重构为iOS原生的导航方式。

## 条件渲染 vs 导航方式对比

### 原有条件渲染方式的问题
- ❌ 所有视图同时存在内存中，内存占用大
- ❌ 代码结构复杂，难以维护
- ❌ 无法利用iOS原生的导航体验
- ❌ 缺乏页面历史和返回功能
- ❌ 难以进行单独的页面测试
- ❌ 不符合iOS用户的使用习惯

### 新导航方式的优势
- ✅ 符合iOS用户习惯，有原生的导航体验
- ✅ 内存管理更好，页面按需加载和释放
- ✅ 代码结构清晰，每个页面独立
- ✅ 支持原生的返回手势和导航
- ✅ 便于单独测试和维护
- ✅ 支持深度链接和状态恢复

## 重构架构

### 1. 导航管理器 (NavigationManager)
```swift
class NavigationManager: ObservableObject {
    @Published var navigationPath = NavigationPath()
    @Published var currentRoute: AppRoute = .splash
    
    // 核心导航方法
    func navigate(to route: AppRoute)      // 导航到新页面
    func replace(with route: AppRoute)     // 替换当前页面
    func goBack()                          // 返回上一页
    func popToRoot()                       // 返回根页面
}
```

### 2. 路由枚举 (AppRoute)
```swift
enum AppRoute: Hashable {
    case splash                    // 启动页
    case login                     // 登录页
    case webView(URL)             // WebView主页
    case callIncoming             // 来电页
    case callOutgoing             // 外呼页
    case callConnected            // 通话页
    case setCallee                // 拨号页
}
```

### 3. 页面组件化
- **SplashView**: 启动页，处理自动登录检查
- **LoginView**: 登录页（保持原有功能）
- **WebViewContainerView**: WebView容器，包含VoIP状态和刷新功能
- **CallIncomingView**: 来电页（保持原有功能）
- **CallOutgoingView**: 外呼页（保持原有功能）
- **CallConnectView**: 通话页（保持原有功能）
- **SetCalleeView**: 拨号页（保持原有功能）

## 重构实现

### 1. ContentView 重构
```swift
struct ContentView: View {
    @StateObject private var navigationManager = NavigationManager()
    
    var body: some View {
        NavigationStack(path: $navigationManager.navigationPath) {
            rootView
                .navigationDestination(for: AppRoute.self) { route in
                    destinationView(for: route)
                }
        }
    }
}
```

### 2. 通知驱动的导航
```swift
// 来电通知 -> 导航到来电页
NotificationCenter.default.addObserver(forName: .incomingCallReceived) { _ in
    navigationManager.handleIncomingCall()
}

// 外呼通知 -> 导航到外呼页
NotificationCenter.default.addObserver(forName: .outgoingCallInitiated) { _ in
    navigationManager.handleOutgoingCall()
}

// 通话连接 -> 导航到通话页
NotificationCenter.default.addObserver(forName: .callConnected) { _ in
    navigationManager.handleCallConnected()
}

// 通话结束 -> 返回上一页
NotificationCenter.default.addObserver(forName: .callEnded) { _ in
    navigationManager.handleCallEnded()
}
```

### 3. 登录成功回调
```swift
// LoginViewModel 中添加导航回调
var onLoginSuccess: ((URL) -> Void)?

// 登录成功时调用
private func navigateToH5HomePage(token: String) {
    // ... 设置 webViewURL
    onLoginSuccess?(url)  // 触发导航
}
```

## 主要改进

### 1. 内存管理优化
- 页面按需创建和销毁
- 避免所有页面同时存在内存中
- 更好的资源管理

### 2. 用户体验提升
- 原生iOS导航体验
- 支持手势返回
- 清晰的页面层级关系

### 3. 代码结构优化
- 每个页面独立的Swift文件
- 清晰的职责分离
- 便于测试和维护

### 4. 状态管理改进
- 集中的导航状态管理
- 通知驱动的页面切换
- 更好的状态同步

## 使用指南

### 1. 添加新页面
```swift
// 1. 在AppRoute中添加新路由
enum AppRoute {
    case newPage
}

// 2. 在NavigationManager中添加导航方法
func navigateToNewPage() {
    navigate(to: .newPage)
}

// 3. 在ContentView中添加目标视图
case .newPage:
    NewPageView()
```

### 2. 页面间导航
```swift
// 导航到新页面
navigationManager.navigate(to: .newPage)

// 替换当前页面
navigationManager.replace(with: .newPage)

// 返回上一页
navigationManager.goBack()
```

### 3. 通知驱动导航
```swift
// 发送通知触发导航
NotificationCenter.default.post(name: .customNotification, object: nil)

// 监听通知并导航
NotificationCenter.default.addObserver(forName: .customNotification) { _ in
    navigationManager.navigateToCustomPage()
}
```

## 后续扩展

这个导航架构为后续功能扩展提供了良好的基础：

1. **深度链接支持**: 可以轻松添加URL scheme处理
2. **状态恢复**: 支持app重启后恢复导航状态
3. **动画定制**: 可以为不同页面切换添加自定义动画
4. **权限控制**: 可以在导航层面添加权限检查
5. **分析统计**: 可以在导航层面添加页面访问统计

这个重构为项目的长期发展奠定了坚实的基础，提供了更好的用户体验和开发体验。
