# VoIP核心功能测试指南

## 功能概述
VoIP是应用的核心功能，实现了以下特性：
1. **强制登录**：只要应用运行，VoIP必须保持登录状态
2. **自动重连**：检测到连接断开时自动重连
3. **状态监控**：实时监控VoIP连接状态
4. **用户提醒**：VoIP不可用时显示状态提醒
5. **手动重试**：用户可手动触发重连

## VoIP状态说明

### 状态类型
- `disconnected`: 未连接
- `connecting`: 连接中
- `connected`: 已连接（正常状态）
- `failed`: 连接失败
- `reconnecting`: 重连中

### 状态指示器
- 🟢 绿色：VoIP正常可用
- 🟠 橙色：VoIP连接中/重连中
- 🔴 红色：VoIP不可用

## 测试场景

### 场景1：正常登录流程
**预期行为：**
1. 用户登录成功后立即尝试VoIP登录
2. VoIP登录成功后显示绿色状态提示"通话功能已就绪"
3. 3秒后自动隐藏成功提示

**测试步骤：**
1. 启动应用并登录
2. 观察VoIP状态变化
3. 验证是否显示成功提示

### 场景2：VoIP账号信息缺失
**预期行为：**
1. 如果用户数据中缺少VoIP账号信息
2. 显示红色状态提示"通话功能暂不可用: 账号信息不完整"
3. 不进行重连尝试

**测试步骤：**
1. 使用没有VoIP信息的测试账号登录
2. 验证错误提示显示

### 场景3：VoIP登录超时
**预期行为：**
1. VoIP登录5秒内未成功
2. 显示红色状态提示"通话功能暂不可用: 登录超时"
3. 启动自动重连机制

**测试步骤：**
1. 断开网络或使用错误的VoIP服务器配置
2. 登录应用
3. 观察超时提示

### 场景4：自动重连机制
**预期行为：**
1. VoIP连接断开后自动检测
2. 启动指数退避重连（2秒、4秒、8秒...最大30秒）
3. 最多重连5次
4. 重连期间显示橙色"重连中"状态

**测试步骤：**
1. 正常登录VoIP
2. 断开网络连接
3. 观察自动重连过程
4. 恢复网络连接验证重连成功

### 场景5：手动重连
**预期行为：**
1. VoIP不可用时显示"重试"按钮
2. 点击重试按钮立即尝试重连
3. 重置重连计数器

**测试步骤：**
1. 等待VoIP连接失败
2. 点击状态提示中的"重试"按钮
3. 验证重连过程

### 场景6：定时状态检查
**预期行为：**
1. 每30秒自动检查VoIP状态
2. 发现异常时自动重连
3. 有账号信息但未登录时自动登录

**测试步骤：**
1. 正常使用应用
2. 在后台断开VoIP连接
3. 等待30秒观察自动检测

## 关键实现点

### 1. 强制VoIP登录
```swift
// 用户登录成功后立即尝试VoIP登录
private func ensureVoIPLogin(userData: UserData) {
    // 检查账号信息完整性
    // 设置VoIP账号信息
    // 尝试登录
    // 启动状态监控
}
```

### 2. 自动重连机制
```swift
private func startAutoReconnect() {
    // 指数退避算法
    let delay = min(pow(2.0, Double(reconnectAttempts)), 30.0)
    // 最大重连次数限制
    guard reconnectAttempts < maxReconnectAttempts else { return }
}
```

### 3. 状态监控
```swift
// 注册状态变化监听
onAccountRegistrationStateChanged: { (core, account, state, message) in
    switch state {
    case .Ok: // 连接成功
    case .Failed: // 连接失败，启动重连
    case .Progress: // 连接中
    }
}
```

### 4. 定时检查
```swift
// 每30秒检查一次状态
Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { _ in
    self.checkVoIPStatus()
}
```

## 日志监控
关注以下关键日志：
- `[CallViewModel] ✅ VoIP登录成功`
- `[CallViewModel] ❌ VoIP登录失败`
- `[CallViewModel] 🔄 第X次重连`
- `[CallViewModel] ⚠️ 检测到VoIP连接异常`
- `[LoginViewModel] ❌ VoIP账号信息不完整`

## 用户体验要点
1. **无感知重连**：用户无需手动干预，系统自动维护连接
2. **状态透明**：用户始终了解VoIP当前状态
3. **快速恢复**：网络恢复后立即重连成功
4. **降级提示**：连接失败时明确告知用户影响
5. **手动控制**：提供手动重试选项
