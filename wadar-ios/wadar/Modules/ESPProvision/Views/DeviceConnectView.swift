//
//  DeviceConnectView.swift
//  wadar
//
//  Created by guan on 2025/7/1.
//  设备连接视图
//

import SwiftUI
import ESPProvision

/// 设备连接视图
/// 显示设备连接状态和WiFi扫描进度
struct DeviceConnectView: View {
    
    @ObservedObject var viewModel: ESPProvisionViewModel
    
    var body: some View {
        VStack(spacing: 24) {
            // 设备连接状态
            DeviceConnectionStatusView(viewModel: viewModel)
            
            // WiFi扫描状态
            if viewModel.provisionState == .connected {
                WiFiScanStatusView(viewModel: viewModel)
            }
            
            // 连接说明
            DeviceConnectionInstructionView()
        }
    }
}

// MARK: - 设备连接状态视图
struct DeviceConnectionStatusView: View {
    @ObservedObject var viewModel: ESPProvisionViewModel
    
    var body: some View {
        VStack(spacing: 16) {
            // 设备信息
            if let device = viewModel.selectedDevice {
                DeviceInfoCardView(device: device, viewModel: viewModel)
            }
            
            // 连接状态
            ConnectionStatusIndicatorView(
                state: viewModel.provisionState,
                statusMessage: viewModel.statusMessage
            )
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 设备信息卡片视图
struct DeviceInfoCardView: View {
    let device: ESPDevice
    let viewModel: ESPProvisionViewModel
    
    var body: some View {
        HStack(spacing: 12) {
            // 设备图标
            ZStack {
                Circle()
                    .fill(Color.blue)
                    .frame(width: 50, height: 50)
                
                Image(systemName: "wifi")
                    .foregroundColor(.white)
                    .font(.system(size: 20))
            }
            
            // 设备信息
            VStack(alignment: .leading, spacing: 4) {
                Text(device.name)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text("安全模式: \(getSecurityDescription(device.security))")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("传输方式: 蓝牙")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
    
    private func getSecurityDescription(_ security: ESPSecurity) -> String {
        switch security {
        case .unsecure:
            return "无加密"
        case .secure:
            return "Sec1"
        case .secure2:
            return "Sec2"
        }
    }
}

// MARK: - 连接状态指示器视图
struct ConnectionStatusIndicatorView: View {
    let state: ESPProvisionState
    let statusMessage: String
    
    var body: some View {
        HStack(spacing: 12) {
            // 状态图标
            Group {
                switch state {
                case .connecting:
                    ProgressView()
                        .scaleEffect(0.8)
                case .connected:
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.system(size: 20))
                default:
                    Image(systemName: "circle")
                        .foregroundColor(.gray)
                        .font(.system(size: 20))
                }
            }
            
            // 状态文本
            VStack(alignment: .leading, spacing: 2) {
                Text(getStateTitle(state))
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                if !statusMessage.isEmpty {
                    Text(statusMessage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
        }
    }
    
    private func getStateTitle(_ state: ESPProvisionState) -> String {
        switch state {
        case .idle:
            return "准备连接"
        case .connecting:
            return "正在连接设备..."
        case .connected:
            return "设备连接成功"
        case .configuring:
            return "配置中..."
        case .success:
            return "连接成功"
        case .failed(_):
            return "连接失败"
        default:
            return "未知状态"
        }
    }
}

// MARK: - WiFi扫描状态视图
struct WiFiScanStatusView: View {
    @ObservedObject var viewModel: ESPProvisionViewModel
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "wifi")
                    .foregroundColor(.blue)
                
                Text("WiFi网络扫描")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Spacer()
                
                if viewModel.availableWiFiNetworks.isEmpty {
                    ProgressView()
                        .scaleEffect(0.6)
                } else {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.system(size: 16))
                }
            }
            
            if !viewModel.availableWiFiNetworks.isEmpty {
                Text("发现 \(viewModel.availableWiFiNetworks.count) 个WiFi网络")
                    .font(.caption)
                    .foregroundColor(.secondary)
            } else {
                Text("正在扫描WiFi网络...")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(8)
    }
}

// MARK: - 设备连接说明视图
struct DeviceConnectionInstructionView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                
                Text("连接说明")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                InstructionItem(text: "正在与设备建立安全连接")
                InstructionItem(text: "连接成功后将自动扫描WiFi网络")
                InstructionItem(text: "请保持设备电源开启")
            }
        }
        .padding()
        .background(Color.blue.opacity(0.05))
        .cornerRadius(8)
    }
}

// MARK: - 说明项视图
struct InstructionItem: View {
    let text: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 6) {
            Text("•")
                .foregroundColor(.blue)
                .font(.caption)
            
            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
                .fixedSize(horizontal: false, vertical: true)
        }
    }
}

// MARK: - 预览
struct DeviceConnectView_Previews: PreviewProvider {
    static var previews: some View {
        DeviceConnectView(viewModel: ESPProvisionViewModel())
            .padding()
    }
}
