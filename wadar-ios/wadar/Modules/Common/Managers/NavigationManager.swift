//
//  NavigationManager.swift
//  wadar
//
//  Created by guan on 2025/6/26.
//

import SwiftUI
import Foundation

// MARK: - 导航管理器
class NavigationManager: NavigationManagerProtocol {
    @Published var navigationPath = NavigationPath()
    @Published var currentRoute: AppRoute = .splash

    // 保存通话前的状态
    private var preCallRoute: AppRoute?
    private var preCallStackDepth: Int = 0

    // 导航到指定路由
    func navigate(to route: AppRoute) {
        print("[NavigationManager] 导航到: \(route.title)")
        currentRoute = route
        navigationPath.append(route)
    }
    
    // 替换当前路由（不添加到导航栈）
    func replace(with route: AppRoute) {
        print("[NavigationManager] 替换当前页面为: \(route.title)")
        currentRoute = route
        // 清空导航栈并设置新的根页面
        navigationPath = NavigationPath()

        // 只有非根页面路由才添加到导航栈
        switch route {
        case .splash, .login, .webView:
            // 这些是根页面，不添加到导航栈
            break
        default:
            navigationPath.append(route)
        }
    }
    
    // 返回上一页
    func goBack() {
        print("[NavigationManager] 返回上一页")
        if !navigationPath.isEmpty {
            navigationPath.removeLast()
        }
    }
    
    // 返回到根页面
    func popToRoot() {
        print("[NavigationManager] 返回到根页面")
        navigationPath = NavigationPath()
        currentRoute = .splash
    }
    
    // 清除所有导航历史
    func reset() {
        print("[NavigationManager] 重置导航状态")
        navigationPath = NavigationPath()
        currentRoute = .splash
    }
    
    // 检查是否可以返回
    var canGoBack: Bool {
        return !navigationPath.isEmpty
    }
    
    // 获取导航栈深度
    var stackDepth: Int {
        return navigationPath.count
    }
}

// MARK: - 导航扩展方法
extension NavigationManager {
    
    // 处理登录成功后的导航
    func handleLoginSuccess(webViewURL: URL) {
        replace(with: .webView(webViewURL))
    }
    
    // 处理登出
    func handleLogout() {
        reset()
        replace(with: .login)
    }
    
    // 处理来电
    func handleIncomingCall() {
        savePreCallState()
        navigate(to: .callIncoming)
    }

    // 处理外呼
    func handleOutgoingCall() {
        savePreCallState()
        navigate(to: .callOutgoing)
    }
    
    // 处理通话连接
    func handleCallConnected() {
        replace(with: .callConnected)
    }
    
    // 处理通话结束 - 简化版本：直接返回WebView首页
    func handleCallEnded() {
        print("[NavigationManager] 处理通话结束，返回WebView首页")
        returnToWebViewHome()
    }

    // 返回WebView首页
    private func returnToWebViewHome() {
        // 获取当前登录状态和WebView URL
        if let webViewURL = getCurrentWebViewURL() {
            print("[NavigationManager] 返回WebView首页: \(webViewURL.absoluteString)")
            replace(with: .webView(webViewURL))
        } else {
            print("[NavigationManager] 无法获取WebView URL，返回登录页面")
            replace(with: .login)
        }

        // 清除保存的通话前状态（如果有的话）
        preCallRoute = nil
        preCallStackDepth = 0
    }

    // 获取当前WebView URL
    private func getCurrentWebViewURL() -> URL? {
        // 首先检查当前路由是否是WebView
        if case .webView(let url) = currentRoute {
            return url
        }

        // 如果当前不是WebView，尝试从保存的状态获取
        if let savedRoute = preCallRoute, case .webView(let url) = savedRoute {
            return url
        }

        // 如果都没有，尝试从LoginViewModel获取
        // 这里需要访问全局的LoginViewModel实例
        return getWebViewURLFromLoginState()
    }

    // 从登录状态获取WebView URL
    private func getWebViewURLFromLoginState() -> URL? {
        // 使用UserManager获取保存的token
        guard let token = UserManager.shared.getToken() else {
            print("[NavigationManager] 无法获取保存的token")
            return nil
        }

        // 使用AppConfig获取base URL并构建WebView URL
        let webviewUrl = AppConfig.shared.webviewUrl
        let urlString = "\(webviewUrl)/pages/auth/app-auth/index?platform=iOS&appType=app&token=\(token)"

        print("[NavigationManager] 重新构建WebView URL: \(urlString)")
        return URL(string: urlString)
    }

    // 保存通话前的状态（保留此方法以备将来扩展使用）
    private func savePreCallState() {
        preCallRoute = currentRoute
        preCallStackDepth = stackDepth
        print("[NavigationManager] 保存通话前状态: \(currentRoute.title), 导航栈深度: \(stackDepth)")
    }
}
