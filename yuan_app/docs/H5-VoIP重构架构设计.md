# H5项目VoIP功能重构架构设计

## 1. 重构目标

### 1.1 现状分析
- **现有问题**：H5项目中存在硬编码的iOS bridge实现
- **技术债务**：VoIP功能与通信层耦合严重，难以维护和扩展
- **兼容性需求**：需要支持Android/iOS双平台，保持向后兼容

### 1.2 重构目标
- 实现分层架构：工具层（通用通信）+ 业务层（VoIP功能）
- 提供通用的跨平台H5-App通信能力
- 支持双向消息传递和页面路由功能
- 保持与现有VoIP功能的完全兼容性

## 2. 新架构设计

### 2.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    H5业务层                                  │
├─────────────────────────────────────────────────────────────┤
│  VoIPService  │  NavigationService  │  其他业务服务...        │
├─────────────────────────────────────────────────────────────┤
│                  AppServiceAdapter                          │
│                   (接口适配层)                               │
├─────────────────────────────────────────────────────────────┤
│                    AppBridge                                │
│                   (通信传输层)                               │
├─────────────────────────────────────────────────────────────┤
│  MessageProtocol  │  ResponseHandler  │  通信协议层          │
├─────────────────────────────────────────────────────────────┤
│              原生App通信接口                                 │
│  WKWebView  │  JavaScriptBridge  │  URLScheme              │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 分层职责

#### 2.2.1 通信协议层 (MessageProtocol + ResponseHandler)
- **职责**：定义标准消息格式，处理消息序列化/反序列化
- **特点**：与iOS端保持一致的消息协议
- **文件**：`utils/bridge/MessageProtocol.js`, `utils/bridge/ResponseHandler.js`

#### 2.2.2 通信传输层 (AppBridge)
- **职责**：管理与原生App的底层通信
- **特点**：支持多种通信方式，自动选择最佳通信方法
- **文件**：`utils/bridge/AppBridge.js`

#### 2.2.3 接口适配层 (AppServiceAdapter)
- **职责**：提供统一的App服务调用接口，处理平台差异
- **特点**：面向业务的API设计，服务注册和发现机制
- **文件**：`utils/bridge/AppServiceAdapter.js`

#### 2.2.4 业务服务层 (VoIPService等)
- **职责**：封装具体业务逻辑，提供面向业务的API
- **特点**：与通信层解耦，易于测试和维护
- **文件**：`utils/services/VoIPService.js`, `utils/services/NavigationService.js`

## 3. VoIP功能重构方案

### 3.1 现有VoIP实现分析
```javascript
// 现有实现 (utils/appBridge.js)
export function callAppVoip(params) {
    // 硬编码的iOS bridge调用
    return VoIPService.makeCall({
        number,
        token,
        displayName: params.displayName
    })
}
```

### 3.2 重构后的VoIP架构
```javascript
// 新的VoIP服务 (utils/services/VoIPService.js)
class VoIPService {
    constructor() {
        this.adapter = AppServiceAdapter
        this.setupEventListeners()
    }
    
    async makeCall(params) {
        return await this.adapter.callService('voip', 'call', params)
    }
    
    async answerCall() {
        return await this.adapter.callService('voip', 'answer')
    }
    
    async rejectCall() {
        return await this.adapter.callService('voip', 'reject')
    }
}
```

### 3.3 消息协议设计
```javascript
// VoIP呼叫消息格式
{
    "id": "msg_12345",
    "type": "request",
    "action": "voip.call",
    "data": {
        "number": "10086",
        "token": "user_token",
        "displayName": "客服热线"
    },
    "timestamp": 1640995200000,
    "platform": "iOS",
    "version": "1.0"
}

// VoIP状态通知格式
{
    "id": "notify_67890",
    "type": "notification", 
    "action": "voip.callConnected",
    "data": {
        "number": "10086",
        "callId": "call_abc123",
        "duration": 0
    },
    "timestamp": 1640995205000,
    "platform": "iOS",
    "version": "1.0"
}
```

## 4. 实现计划

### 4.1 第一阶段：工具层重构
1. 优化现有的 `AppBridge.js` 通信传输层
2. 完善 `MessageProtocol.js` 消息协议
3. 增强 `ResponseHandler.js` 响应处理
4. 改进 `AppServiceAdapter.js` 服务适配

### 4.2 第二阶段：VoIP业务层重构
1. 重构 `VoIPService.js` 业务逻辑
2. 更新 `appBridge.js` 兼容接口
3. 修改页面中的VoIP调用代码
4. 添加错误处理和重连机制

### 4.3 第三阶段：测试和验证
1. 单元测试覆盖
2. 集成测试验证
3. 与iOS端新架构兼容性测试
4. 性能和稳定性测试

## 5. 兼容性保证

### 5.1 向后兼容
- 保留原有的 `callAppVoip()` 接口
- 内部重构为使用新架构
- 添加弃用警告和迁移指南

### 5.2 平台兼容
- 统一的消息协议支持iOS/Android
- 自动检测平台并选择合适的通信方式
- 优雅降级到Web端实现

## 6. 优势总结

### 6.1 架构优势
- **分层清晰**：职责明确，易于维护
- **高度解耦**：业务层与通信层分离
- **易于扩展**：新增功能只需添加对应服务
- **通用性强**：可复用于其他H5应用

### 6.2 开发优势
- **类型安全**：完整的参数验证
- **错误处理**：统一的错误处理机制
- **调试友好**：完整的日志和调试信息
- **测试友好**：每层都可独立测试
